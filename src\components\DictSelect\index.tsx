/**
 * @file 字典选择组件
 * @description 基于dva缓存的字典选择组件，可以在任何地方使用
 * <AUTHOR> Assistant
 * @date 2025-08-29
 */
import { DictionaryState } from '@/models/dictionary';
import { connect } from '@umijs/max';
import { TreeSelect } from 'antd';
import React, { useEffect } from 'react';

interface DictTreeSelectProps {
  type: DictType;
  value?: number;
  onChange?: (value: number) => void;
  placeholder?: string;
  allowClear?: boolean;
  showSearch?: boolean;
  disabled?: boolean;
  style?: React.CSSProperties;
  onlyEnabled?: boolean;
  excludeId?: number;
  dictionary: DictionaryState;
  treeDefaultExpandAll?: boolean;
}

/**
 * 字典树形选择组件
 */
export const DictTreeSelect: React.FC<DictTreeSelectProps> = ({
  type,
  value,
  onChange,
  placeholder,
  allowClear = true,
  showSearch = true,
  disabled = false,
  style,
  excludeId,
  treeDefaultExpandAll = false,
}) => {
  const { convertToTreeSelectOptions, loadDictByType, loading } =
    useDictUtils();

  useEffect(() => {
    loadDictByType(type);
  }, [type, loadDictByType]);

  return (
    <TreeSelect
      value={value}
      onChange={onChange}
      placeholder={
        placeholder ||
        `请选择${
          type === 'region' ? '区域' : type === 'type' ? '类型' : '关系'
        }`
      }
      allowClear={allowClear}
      showSearch={showSearch}
      disabled={disabled}
      loading={loading[type]}
      style={style}
      treeData={convertToTreeSelectOptions(type, undefined, excludeId)}
      treeDefaultExpandAll={treeDefaultExpandAll}
      filterTreeNode={(input, node) =>
        (node.title as string).toLowerCase().includes(input.toLowerCase())
      }
    />
  );
};

/**
 * 字典显示组件
 */
interface DictDisplayProps {
  type: DictType;
  id: number;
  showCode?: boolean;
  showPath?: boolean;
}

export const DictDisplay: React.FC<DictDisplayProps> = ({
  type,
  id,
  showCode = false,
  showPath = false,
}) => {
  const { findDictById, getDictName, getDictPath, loadDictByType } =
    useDictUtils();

  useEffect(() => {
    loadDictByType(type);
  }, [type, loadDictByType]);

  const item = findDictById(type, id);
  const name = getDictName(type, id);
  const path = showPath ? getDictPath(type, id) : [];

  if (!item) {
    return <span>-</span>;
  }

  const getCode = () => {
    return type === 'region'
      ? (item as any).regionCode
      : type === 'type'
      ? (item as any).typeCode
      : (item as any).relationCode;
  };

  const displayText = showCode ? `${name} (${getCode()})` : name;
  const fullText = showPath && path.length > 0 ? path.join(' > ') : displayText;

  return <span title={fullText}>{displayText}</span>;
};

export default {
  DictTreeSelect: connect(
    ({ dictionary }: { dictionary: DictionaryState }) => ({
      dictionary,
    }),
  )(DictTreeSelect),
  DictDisplay: connect(({ dictionary }: { dictionary: DictionaryState }) => ({
    dictionary,
  }))(DictDisplay),
};
